{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "None"}}, "AllowedHosts": "*", "RecordSetting": {"GstVideoEncodeElement": {"arch-x64": "videoconvert ! nvh264enc", "ubuntu.24.04-x64": "videoconvert ! video/x-raw,format=I420 ! nvh264enc", "ubuntu.22.04-x64": "videoconvert ! video/x-raw,format=I420 ! nvh264enc", "ubuntu.18.04-arm64": "nvvidconv ! video/x-raw(memory:NVMM),format=NV12 ! nvv4l2h264enc maxperf-enable=true insert-vui=true preset-level=1 profile=4", "ubuntu.20.04-arm64": "nvvidconv ! nvv4l2h264enc"}, "GstAudioEncodeElementMp4": {"arch-x64": "opusenc ! opusparse", "ubuntu.24.04-x64": "opusenc ! opusparse", "ubuntu.22.04-x64": "opusenc ! opusparse", "ubuntu.18.04-arm64": "voaacenc bitrate=128000 ! aacparse", "ubuntu.20.04-arm64": "voaacenc bitrate=128000 ! aacparse"}, "GstAudioEncodeElementStream": {"arch-x64": "opusenc ! opusparse", "ubuntu.24.04-x64": "opusenc ! opusparse", "ubuntu.22.04-x64": "opusenc ! opusparse", "ubuntu.18.04-arm64": "opusenc ! opusparse", "ubuntu.20.04-arm64": "opusenc ! opusparse"}, "MaxFileSizeMb": 300, "MaxFileLengthSeconds": 1000, "CameraMinWidth": 360, "CameraMinHeight": 360, "CameraMaxSize": 2100000, "CameraOnly16By9": true, "CameraAllowSameBus": true, "SupportedFps": [30, 15, 25], "CamDisplayNamePrefixIncludes": ["USB", "Razer Kiyo X"], "MicDisplayNamePrefixIncludes": ["Takstar", "HyperX Cloud Flight Wireless Mono", "C-Media Electronics", "Unitek Y-247A", "JBL TUNE 310C"], "BitRate": 0}, "Cleanup": {"KeepAvailableGb": 10, "KeepAvailablePercentage": 10, "MonitoringIntervalMinutes": 5, "EnableMessageAlerts": true, "AlertOnStatusChange": true}}