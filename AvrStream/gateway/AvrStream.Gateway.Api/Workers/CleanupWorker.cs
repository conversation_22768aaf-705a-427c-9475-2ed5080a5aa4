using AvrStream.Gateway.Api.Services;
using AvrStream.Gateway.Entities.Databases;
using AvrStream.Gateway.Entities.Models;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Gateway.Api.Workers;

public enum StorageStatus
{
    Normal,
    Warning,
    Emergency
}

public class CleanupWorker : BackgroundService
{
    private readonly ILogger<CleanupWorker> _logger;
    private readonly IConfiguration _configuration;
    private ResourceService _resourceService;
    private readonly IServiceScopeFactory _factory;
    private readonly SystemConfigurationService _systemConfigurationService;
    private readonly RecordService _recordService;
    private readonly MessageService _messageService;

    // Storage status tracking
    private StorageStatus _lastStorageStatus = StorageStatus.Normal;
    private DateTime _lastAlertTime = DateTime.MinValue;
    private readonly TimeSpan _alertCooldown = TimeSpan.FromMinutes(10); // Prevent spam alerts

    public CleanupWorker(IServiceScopeFactory factory, ILogger<CleanupWorker> logger, IConfiguration configuration,
        SystemConfigurationService systemConfigurationService, RecordService recordService, MessageService messageService)
    {
        _factory = factory;
        _logger = logger;
        _configuration = configuration;
        _systemConfigurationService = systemConfigurationService;
        _recordService = recordService;
        _messageService = messageService;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var systemSetting = await _systemConfigurationService.GetSystemSettingAsync();

        // Get configurable monitoring interval
        var monitoringIntervalMinutes = _configuration.GetValue<int>("Cleanup:MonitoringIntervalMinutes", 30);
        var enableMessageAlerts = _configuration.GetValue<bool>("Cleanup:EnableMessageAlerts", true);
        var alertOnStatusChange = _configuration.GetValue<bool>("Cleanup:AlertOnStatusChange", true);

        _logger.LogInformation("Storage monitoring started with {IntervalMinutes} minute intervals. " +
                             "Message alerts: {EnableAlerts}, Status change alerts: {StatusAlerts}",
                             monitoringIntervalMinutes, enableMessageAlerts, alertOnStatusChange);

        if (enableMessageAlerts)
        {
            _messageService.CreateMessage("Storage monitoring service started", MessageType.Info);
        }

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _factory.CreateScope();
                _resourceService = scope.ServiceProvider.GetService<ResourceService>();

                var availableGb = _configuration.GetValue<long?>("Cleanup:KeepAvailableGb");
                var availablePercentage = _configuration.GetValue<int?>("Cleanup:KeepAvailablePercentage");
                var path = systemSetting.RecordPath;

                // Check if storage monitoring is configured
                if (availableGb.HasValue || availablePercentage.HasValue)
                {
                    await PerformStorageMonitoring(path, availableGb, availablePercentage,
                                                 enableMessageAlerts, alertOnStatusChange, stoppingToken);
                }
                else
                {
                    _logger.LogDebug("Storage monitoring not configured. Skipping cleanup checks.");
                }

                await Task.Delay(TimeSpan.FromMinutes(monitoringIntervalMinutes), stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in storage monitoring cycle");
                if (enableMessageAlerts)
                {
                    _messageService.CreateMessage($"Storage monitoring error: {ex.Message}", MessageType.Error);
                }

                // Wait before retrying
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
        }
    }

    public override Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation($"{nameof(CleanupWorker)} stopped");
        return base.StopAsync(cancellationToken);
    }

    /// <summary>
    /// Performs comprehensive storage monitoring with message alerts
    /// </summary>
    private async Task PerformStorageMonitoring(string path, long? availableGb, int? availablePercentage,
        bool enableMessageAlerts, bool alertOnStatusChange, CancellationToken stoppingToken)
    {
        var info = _resourceService.GetStorageInfo(path);
        var availableGbActual = info.available / (1024.0 * 1024.0 * 1024.0);
        var availablePercentageActual = info.available * 100.0 / info.total;

        _logger.LogInformation(
            "Storage status: {AvailableGb:F2} GB ({AvailablePercent:F1}%) available of {TotalGb:F2} GB total. " +
            "Thresholds: {ThresholdGb} GB, {ThresholdPercent}%",
            availableGbActual, availablePercentageActual, info.total / (1024.0 * 1024.0 * 1024.0),
            availableGb, availablePercentage);

        // Determine current storage status
        var currentStatus = DetermineStorageStatus(info, availableGb, availablePercentage);

        // Handle status changes and alerts
        await HandleStorageStatusChange(currentStatus, availableGbActual, availablePercentageActual,
                                      enableMessageAlerts, alertOnStatusChange);

        // Perform appropriate action based on status
        switch (currentStatus)
        {
            case StorageStatus.Emergency:
                _logger.LogCritical("EMERGENCY: Storage critically low! Initiating emergency cleanup...");
                await PerformEmergencyCleanup(enableMessageAlerts, stoppingToken);
                break;

            case StorageStatus.Warning:
                _logger.LogWarning("WARNING: Storage threshold reached. Performing normal cleanup...");
                await PerformNormalCleanup(enableMessageAlerts, stoppingToken);
                break;

            case StorageStatus.Normal:
                _logger.LogDebug("Storage levels are normal. No cleanup needed.");
                break;
        }

        _lastStorageStatus = currentStatus;
    }

    /// <summary>
    /// Determines the current storage status based on thresholds
    /// </summary>
    private StorageStatus DetermineStorageStatus((long total, long available) info, long? availableGb, int? availablePercentage)
    {
        // Emergency mode: storage < availableGb OR < availablePercentage
        var emergencyGb = availableGb.HasValue && info.available < availableGb.Value * 1024 * 1024 * 1024;
        var emergencyPercentage = availablePercentage.HasValue &&
                                 info.available * 100.0 / info.total < availablePercentage.Value;

        if (emergencyGb || emergencyPercentage)
        {
            return StorageStatus.Emergency;
        }

        // Warning mode: approaching thresholds (120% of threshold)
        var warningGb = availableGb.HasValue && info.available < availableGb.Value * 1024 * 1024 * 1024 * 1.2;
        var warningPercentage = availablePercentage.HasValue &&
                               info.available * 100.0 / info.total < availablePercentage.Value * 1.2;

        if (warningGb || warningPercentage)
        {
            return StorageStatus.Warning;
        }

        return StorageStatus.Normal;
    }

    /// <summary>
    /// Handles storage status changes and sends appropriate alerts
    /// </summary>
    private async Task HandleStorageStatusChange(StorageStatus currentStatus, double availableGbActual,
        double availablePercentageActual, bool enableMessageAlerts, bool alertOnStatusChange)
    {
        var now = DateTime.UtcNow;
        var shouldAlert = enableMessageAlerts &&
                         (currentStatus != _lastStorageStatus ||
                          (currentStatus == StorageStatus.Emergency && now - _lastAlertTime > _alertCooldown));

        if (!shouldAlert) return;

        string message;
        MessageType messageType;

        switch (currentStatus)
        {
            case StorageStatus.Emergency:
                message = $"CRITICAL: Storage emergency! Only {availableGbActual:F2} GB ({availablePercentageActual:F1}%) available. Emergency cleanup initiated.";
                messageType = MessageType.Error;
                break;

            case StorageStatus.Warning:
                message = $"WARNING: Storage running low. {availableGbActual:F2} GB ({availablePercentageActual:F1}%) available. Cleanup in progress.";
                messageType = MessageType.Alert;
                break;

            case StorageStatus.Normal:
                if (_lastStorageStatus != StorageStatus.Normal)
                {
                    message = $"INFO: Storage levels normalized. {availableGbActual:F2} GB ({availablePercentageActual:F1}%) available.";
                    messageType = MessageType.Info;
                }
                else
                {
                    return; // Don't send normal status alerts repeatedly
                }
                break;

            default:
                return;
        }

        _messageService.CreateMessage(message, messageType);
        _lastAlertTime = now;

        _logger.LogInformation("Storage alert sent: {Message}", message);
    }

    /// <summary>
    /// Performs emergency cleanup: stops all pipelines, deletes all records, restarts pipelines
    /// </summary>
    private async Task PerformEmergencyCleanup(bool enableMessageAlerts, CancellationToken stoppingToken)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        try
        {
            _logger.LogCritical("=== EMERGENCY CLEANUP PROCEDURE STARTED ===");

            if (enableMessageAlerts)
            {
                _messageService.CreateMessage("Emergency storage cleanup started - stopping all recordings", MessageType.Error);
            }

            // Step 1: Stop all pipelines
            _logger.LogCritical("Step 1/4: Stopping all recording pipelines...");
            var stopStartTime = System.Diagnostics.Stopwatch.StartNew();
            await _recordService.StopAllAsync(stoppingToken);
            stopStartTime.Stop();
            _logger.LogInformation("All pipelines stopped in {ElapsedMs} ms", stopStartTime.ElapsedMilliseconds);

            // Step 2: Delete all records
            _logger.LogCritical("Step 2/4: Deleting ALL recording files...");
            var cleanupStartTime = System.Diagnostics.Stopwatch.StartNew();
            var deletedCount = await CleanupAllRecords();
            cleanupStartTime.Stop();
            _logger.LogCritical("Deleted {DeletedCount} files in {ElapsedMs} ms", deletedCount, cleanupStartTime.ElapsedMilliseconds);

            // Step 3: Wait for cleanup
            _logger.LogInformation("Step 3/4: Waiting for file system cleanup...");
            await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);

            // Step 4: Restart pipelines
            _logger.LogCritical("Step 4/4: Restarting all recording pipelines...");
            var startStartTime = System.Diagnostics.Stopwatch.StartNew();
            await _recordService.StartAll();
            startStartTime.Stop();
            _logger.LogInformation("All pipelines restarted in {ElapsedMs} ms", startStartTime.ElapsedMilliseconds);

            stopwatch.Stop();
            _logger.LogCritical("=== EMERGENCY CLEANUP COMPLETED in {TotalMs} ms ===", stopwatch.ElapsedMilliseconds);

            if (enableMessageAlerts)
            {
                _messageService.CreateMessage($"Emergency cleanup completed. Deleted {deletedCount} files. Recordings resumed.", MessageType.Info);
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogCritical(ex, "=== EMERGENCY CLEANUP FAILED after {TotalMs} ms ===", stopwatch.ElapsedMilliseconds);

            if (enableMessageAlerts)
            {
                _messageService.CreateMessage($"Emergency cleanup failed: {ex.Message}", MessageType.Error);
            }
            throw;
        }
    }

    /// <summary>
    /// Performs normal cleanup: removes oldest synchronized records until threshold is met
    /// </summary>
    private async Task PerformNormalCleanup(bool enableMessageAlerts, CancellationToken stoppingToken)
    {
        var retry = 0;
        var maxRetries = 20;
        var deletedCount = 0;

        _logger.LogInformation("Starting normal cleanup (oldest synchronized records only)...");

        while (retry < maxRetries && !stoppingToken.IsCancellationRequested)
        {
            var result = await CleanupOldestSynchronizedRecord();
            if (result.success)
            {
                deletedCount += result.deletedCount;
                if (result.deletedCount == 0)
                {
                    _logger.LogInformation("No more synchronized records to clean up");
                    break;
                }
            }
            else
            {
                _logger.LogWarning("Normal cleanup iteration failed, retrying...");
            }

            await Task.Delay(TimeSpan.FromSeconds(15), stoppingToken);
            retry++;
        }

        if (retry >= maxRetries)
        {
            _logger.LogWarning("Normal cleanup reached maximum retry limit ({MaxRetries})", maxRetries);
        }

        _logger.LogInformation("Normal cleanup completed. Deleted {DeletedCount} synchronized files", deletedCount);

        if (enableMessageAlerts && deletedCount > 0)
        {
            _messageService.CreateMessage($"Storage cleanup completed. Removed {deletedCount} old recording files.", MessageType.Info);
        }
    }

    /// <summary>
    /// Deletes ALL recording files (emergency cleanup)
    /// </summary>
    private async Task<int> CleanupAllRecords()
    {
        var deletedCount = 0;
        try
        {
            using var scope = _factory.CreateScope();
            var db = scope.ServiceProvider.GetService<GatewayDbContext>();

            var recordingFiles = await db.RecordingFiles.AsNoTracking().ToListAsync();
            _logger.LogWarning("Found {FileCount} total recording files for emergency cleanup", recordingFiles.Count);

            foreach (var recordingFile in recordingFiles)
            {
                try
                {
                    if (File.Exists(recordingFile.Path))
                    {
                        File.Delete(recordingFile.Path);
                        _logger.LogDebug("Deleted file: {FilePath}", recordingFile.Path);
                    }

                    db.Remove(recordingFile);
                    deletedCount++;
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Failed to delete file: {FilePath}", recordingFile.Path);
                }
            }

            await db.SaveChangesAsync();
            return deletedCount;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error during emergency cleanup");
            return deletedCount;
        }
    }

    /// <summary>
    /// Deletes oldest synchronized recording files (normal cleanup)
    /// </summary>
    private async Task<(bool success, int deletedCount)> CleanupOldestSynchronizedRecord()
    {
        var deletedCount = 0;
        try
        {
            using var scope = _factory.CreateScope();
            var db = scope.ServiceProvider.GetService<GatewayDbContext>();

            // Only delete synchronized files in normal mode
            var recordingFiles = await db.RecordingFiles.AsNoTracking()
                .Where(f => f.IsSynchronized == true)
                .OrderBy(f => f.CreatedDate)
                .Take(10) // Process in batches
                .ToListAsync();

            if (!recordingFiles.Any())
            {
                return (true, 0); // No synchronized files to delete
            }

            _logger.LogDebug("Found {FileCount} synchronized files for normal cleanup", recordingFiles.Count);

            foreach (var recordingFile in recordingFiles)
            {
                try
                {
                    if (File.Exists(recordingFile.Path))
                    {
                        File.Delete(recordingFile.Path);
                        _logger.LogDebug("Deleted synchronized file: {FilePath}", recordingFile.Path);
                    }

                    db.Remove(recordingFile);
                    deletedCount++;
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Failed to delete synchronized file: {FilePath}", recordingFile.Path);
                }
            }

            await db.SaveChangesAsync();
            return (true, deletedCount);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error during normal cleanup");
            return (false, deletedCount);
        }
    }
}